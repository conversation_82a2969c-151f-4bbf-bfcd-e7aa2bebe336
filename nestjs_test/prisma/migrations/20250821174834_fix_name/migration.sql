/*
  Warnings:

  - You are about to drop the `product_map` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."product_map" DROP CONSTRAINT "product_map_userId_fkey";

-- DropTable
DROP TABLE "public"."product_map";

-- CreateTable
CREATE TABLE "public"."product_cart" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_cart_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "product_cart_userId_key" ON "public"."product_cart"("userId");

-- AddForeignKey
ALTER TABLE "public"."product_cart" ADD CONSTRAINT "product_cart_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
