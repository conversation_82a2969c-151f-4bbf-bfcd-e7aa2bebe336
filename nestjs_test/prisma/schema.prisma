
generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
}

model User {
  id       Int    @id @default(autoincrement())
  email    String @unique
  name     String
  password String
  role     Role     @default(USER)
  productCart ProductCart?

  @@map("user")
}

model Company {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  description String?
  email       String    @unique
  phone       String?
  address     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[] // Products owned by the company
  ownerId     Int
  owner       User      @relation(fields: [ownerId], references: [id]) // Company owned by a user

  @@map("company")
}

model Product {
  id Int @id @default(autoincrement())
  name String
  description String
  price Int
  categoryId Int?
  category Category? @relation(fields: [categoryId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product")
}

model ProductCart {
  id Int @id @default(autoincrement())
  userId Int @unique
  user User @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product_map")
}

model Category {
  id Int @id @default(autoincrement())
  name String @unique
  description String?
  products Product[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("category")
}
