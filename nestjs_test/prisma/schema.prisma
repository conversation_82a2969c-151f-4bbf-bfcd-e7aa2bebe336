
generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
  VENDOR
}

model User {
  id       Int    @id @default(autoincrement())
  email    String @unique
  name     String
  password String
  role     Role     @default(USER)
  productCart ProductCart?
  ownedCompanies Company[]

  @@map("user")
}

model Company {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  description String?
  email       String    @unique
  phone       String?
  address     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
  ownerId     Int
  owner       User      @relation(fields: [ownerId], references: [id])

  @@map("company")
}

model ProductCart {
  id Int @id @default(autoincrement())
  userId Int @unique
  user User @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product_cart")
}

model Product {
  id Int @id @default(autoincrement())
  name String
  description String
  price Int
  categoryId Int?
  category Category? @relation(fields: [categoryId], references: [id])
  companyId Int?
  company Company? @relation(fields: [companyId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product")
}

model Category {
  id Int @id @default(autoincrement())
  name String @unique
  description String?
  products Product[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("category")
}

